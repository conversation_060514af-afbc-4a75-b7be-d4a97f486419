{"name": "gcc-xkd", "version": "2.3.1", "private": true, "description": "西安科技大学高层次人才服务管理平台", "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "build": "cross-env API_ENV=production umi build", "build:test": "cross-env API_ENV=dev umi build", "lint": "eslint --ext .js src mock tests && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js", "lint:fix": "eslint --fix --ext .js src mock tests && stylelint --fix 'src/**/*.less' --syntax less", "lint:prettier": "check-prettier lint", "lint:style": "stylelint 'src/**/*.less' --syntax less", "prettier": "node ./scripts/prettier.js", "start": "cross-env MOCK=none umi dev", "start:test": "cross-env MOCK=none API_ENV=dev umi dev", "start:mock": "cross-env ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION=site umi dev", "test": "umi test", "test:all": "node ./tests/run-tests.js", "test:component": "umi test ./src/components", "tslint": "npm run tslint:fix", "tslint:fix": "tslint --fix 'src/**/*.ts*'"}, "husky": {"hooks": {"pre-commit": "npm run lint-staged"}}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx}": "npm run lint-staged:js", "**/*.{js,ts,tsx,json,jsx,less}": ["node ./scripts/lint-prettier.js", "git add"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"antd": "^3.17.0", "bizcharts": "^3.4.3", "bizcharts-plugin-slider": "^2.1.1-beta.1", "classnames": "^2.2.6", "dva": "^2.4.1", "enquire-js": "^0.2.1", "express": "^4.16.4", "gg-editor": "^2.0.2", "lodash": "^4.17.11", "lodash-decorators": "^6.0.1", "memoize-one": "^5.0.0", "moment": "^2.24.0", "netlify-lambda": "^1.4.3", "numeral": "^2.0.6", "nzh": "^1.0.4", "omit.js": "^1.0.0", "path-to-regexp": "^3.0.0", "prop-types": "^15.6.2", "qs": "^6.6.0", "rc-animate": "^2.6.0", "react": "^16.7.0", "react-container-query": "^0.11.0", "react-copy-to-clipboard": "^5.0.1", "react-document-title": "^2.0.3", "react-dom": "^16.7.0", "react-fittext": "^1.0.0", "react-html-table-to-excel": "^2.0.0", "react-media": "^1.9.2", "umi": "^2.7.3", "umi-plugin-react": "^1.7.2", "umi-request": "^1.0.5"}, "devDependencies": {"@types/history": "^4.7.2", "@types/react": "^16.8.1", "@types/react-dom": "^16.0.11", "antd-pro-merge-less": "^1.0.0", "antd-theme-webpack-plugin": "^1.2.0", "babel-eslint": "^10.0.1", "chalk": "^2.4.2", "check-prettier": "^1.0.1", "cross-env": "^5.2.0", "cross-port-killer": "^1.0.1", "enzyme": "^3.9.0", "eslint": "^5.13.0", "eslint-config-airbnb": "^17.1.0", "eslint-config-prettier": "^4.1.0", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-compat": "^2.6.3", "eslint-plugin-import": "^2.16.0", "eslint-plugin-jsx-a11y": "^6.2.1", "eslint-plugin-markdown": "^1.0.0", "eslint-plugin-react": "^7.12.4", "extract-text-webpack-plugin": "^4.0.0-beta.0", "gh-pages": "^2.0.1", "husky": "^1.3.1", "jest-puppeteer": "^4.1.0", "less": "^3.9.0", "lint-staged": "^8.1.1", "merge-umi-mock-data": "^2.0.6", "mockjs": "^1.0.1-beta3", "prettier": "^1.16.4", "serverless-http": "^1.9.1", "slash2": "^2.0.0", "stylelint": "^9.10.1", "stylelint-config-css-modules": "^1.3.0", "stylelint-config-prettier": "^5.0.0", "stylelint-config-rational-order": "^0.1.0", "stylelint-config-standard": "^18.2.0", "stylelint-declaration-block-no-ignored-properties": "^1.1.0", "stylelint-order": "^2.0.0", "tslint": "^5.12.1", "tslint-config-prettier": "^1.17.0", "tslint-react": "^3.6.0", "umi-plugin-ga": "^1.1.3"}, "optionalDependencies": {"puppeteer": "^1.14.0"}, "engines": {"node": ">=8.0.0"}, "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"]}