/* eslint-disable camelcase */
import React, { PureComponent } from 'react';
import { Button, Icon, Upload, message } from 'antd';

function beforeUpload(file) {
  // const isJPG = file.type === 'image/jpeg';
  // if (!isJPG) {
  //   message.error('You can only upload JPG file!');
  // }
  const isLt50M = file.size / 1024 / 1024 < 50;
  if (!isLt50M) {
    message.error('上传文件大小不得超过50MB!');
  }
  // return isJPG && isLt2M;
  return isLt50M;
}

export default class MyUpload extends PureComponent {
  constructor(props) {
    super(props);
    const { fileList } = props;
    this.state = {
      fileList: fileList || [],
    };
  }

  // state = {
  //   fileList: [
  //     {
  //       uid: '-1',
  //       name: 'xxx.png',
  //       status: 'done',
  //       url: 'http://www.baidu.com/xxx.png',
  //     },
  //   ],
  // };

  handleChange = info => {
    const { disabled, onSuccess, onError } = this.props;
    const {
      file: { status, response },
      fileList,
    } = info;
    console.log(status);
    if (status === 'done') {
      if (response.status === 'ok') {
        message.success(`${info.file.name} 上传成功。`);
        if (onSuccess && typeof onSuccess === 'function') onSuccess(response);
      } else {
        message.error(`上传失败，错误码： ${response.message}`);
        if (onError && typeof onError === 'function') onError(response);
      }
    } else if (status === 'error') {
      message.error(`${info.file.name} 上传失败.`);
      if (onError && typeof onError === 'function') onError(response);
    }

    let fileList_new = [...fileList];

    // 1. Limit the number of uploaded files
    // Only to show one recent uploaded files, and old ones will be replaced by the new
    fileList_new = fileList.slice(-1);

    // 2. Read from response and show file link
    fileList_new = fileList.map(file => {
      const f = file;
      if (file.response) {
        // Component will show file.url as link
        f.url = file.response.url;
      }
      return f;
    });

    // 禁用状态下始终只显示初始值
    if (disabled) {
      this.setState({ fileList });
    } else {
      this.setState({ fileList: fileList_new });
    }
  };

  render() {
    const { fileList } = this.state;

    const {
      disabled,
      action = '/server/api/upload',
      btnText = '点击上传',
    } = this.props;

    const props = {
      disabled,
      name: 'file',
      headers: {
        authorization: 'authorization-text',
      },
      action,
      beforeUpload,
      onChange: this.handleChange,
    };

    return (
      <Upload {...props} fileList={fileList}>
        <Button style={disabled ? { display: 'none' } : {}}>
          <Icon type="upload" /> {btnText}
        </Button>
      </Upload>
    );
  }
}
