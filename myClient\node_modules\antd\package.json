{"name": "antd", "version": "3.26.20", "description": "An enterprise-class UI design language and React components implementation", "keywords": ["ant", "component", "components", "design", "framework", "frontend", "react", "react-component", "ui"], "homepage": "http://ant.design/", "bugs": {"url": "https://github.com/ant-design/ant-design/issues"}, "repository": {"type": "git", "url": "https://github.com/ant-design/ant-design"}, "license": "MIT", "contributors": ["ant"], "files": ["dist", "lib", "es"], "sideEffects": ["dist/*", "es/**/style/*", "lib/**/style/*", "*.less"], "main": "lib/index.js", "module": "es/index.js", "unpkg": "dist/antd.min.js", "typings": "lib/index.d.ts", "scripts": {"api-collection": "antd-tools run api-collection", "authors": "git log --format='%aN <%aE>' | sort -u | grep -v 'users.noreply.github.com' | grep -v 'gitter.im' | grep -v '.local>' | grep -v 'alibaba-inc.com' | grep -v 'alipay.com' | grep -v 'taobao.com' > AUTHORS.txt", "build": "npm run compile && npm run dist", "bundlesize": "bundlesize", "check-commit": "node ./scripts/check-commit.js", "compile": "antd-tools run compile", "deploy": "echo '!!! v3 is in maintaining which means no need to deploy site !!!'", "dist": "antd-tools run dist", "lint": "npm run lint:tsc && npm run lint:script && npm run lint:demo && npm run lint:style && npm run lint:deps", "lint-fix": "npm run lint-fix:script && npm run lint-fix:demo && npm run lint-fix:style", "lint-fix:demo": "eslint-tinker ./components/*/demo/*.md", "lint-fix:script": "npm run lint:script -- --fix", "lint-fix:style": "npm run lint:style -- --fix", "lint:demo": "cross-env RUN_ENV=DEMO eslint components/*/demo/*.md --ext '.md'", "lint:deps": "antd-tools run deps-lint", "lint:md": "remark components/", "lint:script": "eslint . --ext '.js,.jsx,.ts,.tsx'", "lint:style": "stylelint '{site,components}/**/*.less' --syntax less", "lint:tsc": "npm run tsc", "pre-publish": "npm run check-commit && npm run test-all", "prettier": "prettier -c --write '**/*'", "pretty-quick": "pretty-quick", "pub": "antd-tools run pub", "prepublish": "antd-tools run guard", "site": "cross-env NODE_ICU_DATA=node_modules/full-icu bisheng build --ssr -c ./site/bisheng.config.js && node ./scripts/generateColorLess.js", "sort": "npx sort-package-json", "sort-api": "antd-tools run sort-api-table", "start": "rimraf _site && mkdir _site && node ./scripts/generateColorLess.js && cross-env NODE_ENV=development bisheng start -c ./site/bisheng.config.js", "start:preact": "node ./scripts/generateColorLess.js && cross-env NODE_ENV=development REACT_ENV=preact bisheng start -c ./site/bisheng.config.js", "test": "jest --config .jest.js --no-cache", "test-all": "./scripts/test-all.sh", "test-node": "jest --config .jest.node.js --no-cache", "tsc": "tsc", "site:test": "jest --config .jest.site.js --cache=false"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "browserslist": ["last 2 version", "Firefox ESR", "> 1%", "ie >= 9"], "dependencies": {"@ant-design/create-react-context": "^0.2.4", "@ant-design/icons": "~2.1.1", "@ant-design/icons-react": "~2.0.1", "@types/react-slick": "^0.23.4", "array-tree-filter": "^2.1.0", "babel-runtime": "6.x", "classnames": "~2.2.6", "copy-to-clipboard": "^3.2.0", "css-animation": "^1.5.0", "dom-closest": "^0.2.0", "enquire.js": "^2.1.6", "is-mobile": "^2.1.0", "lodash": "^4.17.13", "moment": "^2.24.0", "omit.js": "^1.0.2", "prop-types": "^15.7.2", "raf": "^3.4.1", "rc-animate": "^2.10.2", "rc-calendar": "~9.15.7", "rc-cascader": "~0.17.4", "rc-checkbox": "~2.1.6", "rc-collapse": "~1.11.3", "rc-dialog": "~7.6.0", "rc-drawer": "~3.1.1", "rc-dropdown": "~2.4.1", "rc-editor-mention": "^1.1.13", "rc-form": "^2.4.10", "rc-input-number": "~4.5.0", "rc-mentions": "~0.4.0", "rc-menu": "~7.5.1", "rc-notification": "~3.3.1", "rc-pagination": "~1.20.11", "rc-progress": "~2.5.0", "rc-rate": "~2.5.0", "rc-resize-observer": "^0.1.0", "rc-select": "~9.2.0", "rc-slider": "~8.7.1", "rc-steps": "~3.5.0", "rc-switch": "~1.9.0", "rc-table": "~6.10.5", "rc-tabs": "~9.7.0", "rc-time-picker": "~3.7.1", "rc-tooltip": "~3.7.3", "rc-tree": "~2.1.0", "rc-tree-select": "~2.9.1", "rc-trigger": "^2.6.2", "rc-upload": "~2.9.1", "rc-util": "^4.16.1", "react-lazy-load": "^3.0.13", "react-lifecycles-compat": "^3.0.4", "react-slick": "~0.25.2", "resize-observer-polyfill": "^1.5.1", "shallowequal": "^1.1.0", "warning": "~4.0.3"}, "devDependencies": {"@ant-design/colors": "^3.2.2", "@ant-design/tools": "^9.0.1", "@qixian.cs/github-contributors-list": "^1.0.3", "@sentry/browser": "^5.4.0", "@stackblitz/sdk": "^1.3.0", "@types/classnames": "^2.2.8", "@types/gtag.js": "^0.0.3", "@types/jest": "^24.0.23", "@types/lodash": "^4.14.139", "@types/prop-types": "^15.7.1", "@types/raf": "^3.4.0", "@types/react": "^16.9.0", "@types/react-dom": "^16.8.4", "@types/shallowequal": "^1.1.1", "@types/warning": "^3.0.0", "@typescript-eslint/eslint-plugin": "^2.0.0", "@typescript-eslint/parser": "~2.23.0", "antd-theme-generator": "^1.1.6", "babel-eslint": "^10.0.1", "babel-plugin-add-react-displayname": "^0.0.5", "bisheng": "^1.3.3", "bisheng-plugin-antd": "^1.3.1", "bisheng-plugin-description": "^0.1.4", "bisheng-plugin-react": "^1.0.0", "bisheng-plugin-toc": "^0.4.4", "bundlesize": "^0.18.0", "chalk": "^3.0.0", "cheerio": "^1.0.0-rc.3", "cross-env": "^6.0.0", "css-split-webpack-plugin": "^0.2.6", "dekko": "^0.2.1", "docsearch.js": "^2.6.3", "enquire-js": "^0.2.1", "enzyme": "^3.10.0", "enzyme-adapter-react-16": "^1.14.0", "enzyme-to-json": "^3.3.5", "eslint": "^6.1.0", "eslint-config-airbnb": "^18.0.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-import": "~2.20.1", "eslint-plugin-jest": "^23.0.2", "eslint-plugin-jsx-a11y": "^6.2.1", "eslint-plugin-markdown": "^1.0.0", "eslint-plugin-react": "^7.14.2", "eslint-tinker": "^0.5.0", "fetch-jsonp": "^1.1.3", "full-icu": "^1.3.0", "glob": "^7.1.4", "http-server": "^0.12.0", "husky": "^3.0.2", "immutability-helper": "^3.0.0", "intersection-observer": "^0.7.0", "jest": "^25.5.0", "jsdom": "^15.1.1", "jsonml.js": "^0.1.0", "logrocket": "^1.0.0", "logrocket-react": "^4.0.0", "lz-string": "^1.4.4", "mockdate": "^2.0.2", "node-fetch": "^2.6.0", "preact": "^10.0.0", "preact-compat": "^3.18.5", "prettier": "^1.17.1", "pretty-quick": "^2.0.0", "querystring": "^0.2.0", "rc-footer": "^0.6.0", "rc-queue-anim": "^1.6.12", "rc-scroll-anim": "^2.5.8", "rc-tween-one": "^2.4.1", "react": "^16.5.2", "react-color": "^2.17.3", "react-copy-to-clipboard": "^5.0.1", "react-dnd": "^11.1.1", "react-dnd-html5-backend": "^11.1.1", "react-dom": "^16.5.2", "react-github-button": "^0.1.11", "react-helmet-async": "^1.0.4", "react-highlight-words": "^0.16.0", "react-infinite-scroller": "^1.2.4", "react-intl": "^3.1.1", "react-resizable": "^1.8.0", "react-router": "^3.2.3", "react-router-dom": "^5.0.1", "react-sticky": "^6.0.3", "react-test-renderer": "^16.8.6", "react-virtualized": "~9.21.1", "reqwest": "^2.0.5", "rimraf": "^3.0.0", "scrollama": "^2.0.0", "simple-git": "^1.113.0", "stylelint": "^12.0.0", "stylelint-config-prettier": "^8.0.0", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^19.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.1.0", "stylelint-order": "^4.0.0", "typescript": "~3.8.3", "xhr-mock": "^2.4.1", "xhr2": "^0.2.0", "yaml-front-matter": "^4.0.0"}, "peerDependencies": {"react": ">=16.0.0", "react-dom": ">=16.0.0"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "bundlesize": [{"path": "./dist/antd.min.js", "maxSize": "540 kB"}, {"path": "./dist/antd.min.css", "maxSize": "60 kB"}], "title": "Ant Design", "resolutions": {"typescript": "3.8.3"}}